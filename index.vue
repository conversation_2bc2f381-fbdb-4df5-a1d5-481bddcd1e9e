<template>
  <main class="main">
    <div class="que-content">
      <RjScrollFor
        ref="scrollRef"
        v-if="handleQList?.length > 0"
        :items="handleQList"
        itemKey="qId"
        :isExtraStyle="false"
      >
      </RjScrollFor>
    </div>
  </main>
</template>

<script>
export default {
  data() {
    return {
      handleQList: [
        { qId: 1, content: 'pdf 1' },
        { qId: 2, content: 'pdf 2' },
        { qId: 3, content: 'pdf 3' },
        { qId: 4, content: 'pdf 4' },
      ],
    }
  },
}
</script>
