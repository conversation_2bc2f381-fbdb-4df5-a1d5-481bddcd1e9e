<template>
  <div class="scroll-for-wrapper" ref="ScrollForWrapper" @scroll.passive="handleScroll" v-rj-scroll="scrollBinding">
    <component
      v-for="(item, index) in beforeBlanks"
      class="scroll-for-item"
      :is="blankComponent"
      :key="index"
      :item="item"
      :id="`blankIdPrefix${index}`"
      :style="getBlankViewStyle(index)"
    ></component>
    <RjScrollForItem
      v-for="itemView in itemViews"
      v-show="itemView.nr.isUsed"
      :class="itemView.nr.isUsed ? 'scroll-for-item' : undefined"
      :key="itemView.nr.id"
      :id="itemView.nr.id"
      :index="itemView.nr.index"
      :style="itemView.nr.size ? `min-height: ${itemView.nr.size.height}px` : undefined"
    >
      <slot :item="itemView.item" :index="itemView.nr.index"> </slot>
    </RjScrollForItem>
    <component
      v-for="(item, index) in afterBlanks"
      :is="blankComponent"
      class="scroll-for-item"
      :key="index + endIndex + 1"
      :item="item"
      :id="`blankIdPrefix${index + endIndex + 1}`"
      :style="getBlankViewStyle(index + endIndex + 1)"
    ></component>
  </div>
</template>

<script>
import RjBlankComponent from './virtualScroll/RjBlankComponent.vue'

// const view = {
//   nr: {
//     id: '',
//     key: '',
//     index: 0,
//   },
//   item: {},
// }

let uid = 0
const componentIdPrefix = 'c-id-'
const blankIdPrefix = 'b-id-'

export default {
  props: {
    items: {
      type: Array,
      required: true,
    },
    itemKey: {
      type: String,
    },
    scrollBinding: {
      type: Object,
    },
    buffer: {
      type: Number,
      default: 100,
    },
    blankComponent: {
      type: Object,
      default: () => {
        return RjBlankComponent
      },
    },
    isExtraStyle: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      itemViews: [],
      startIndex: -1,
      endIndex: -1,
      itemIntersectionObserver: undefined,
      itemWrapperBound: undefined,
      blankIdPrefix: blankIdPrefix,
      itemSizes: undefined,
    }
  },
  computed: {
    beforeBlanks() {
      if (this.startIndex < 0) {
        return this.items.slice(0, this.items.length)
      }
      return this.items.slice(0, this.startIndex)
    },
    afterBlanks() {
      if (this.endIndex < 0) {
        return []
      }
      return this.items.slice(this.endIndex + 1)
    },
  },
  watch: {
    items: {
      handler(val, oldVal) {
        // 根据索引范围重新构建
        const itemViews = this.itemViews
        this.startIndex = -1
        this.endIndex = -1
        this._itemSizes = []
        for (let i = 0; i < itemViews.length; i++) {
          const view = itemViews[i]
          if (!view.nr.isUsed) {
            continue
          }
          this.unuseView(view)
        }
        this.$nextTick(() => {
          this.updateVisibleItems(true)
        })
      },
    },
  },
  created() {
    this.initPool()
  },
  mounted() {
    this._resizeObserver = new ResizeObserver(entries => {
      this.handleResize()
      this.updateVisibleItems()
    })
    //初始化内容
    this._resizeObserver.observe(this.$refs.ScrollForWrapper)

    this.handleResize()
  },
  beforeDestroy() {
    this._resizeObserver.unobserve(this.$refs.ScrollForWrapper)
  },
  methods: {
    scrollIntoView(index, behavior) {
      if (index < 0 || index >= this.items.length) {
        return
      }
      const itemNodes = this.getVisibleChildren()
      itemNodes[index]?.scrollIntoView({ behavior })
    },

    clearExtraStyle(index) {
      const items = this.items
      if (index < 0 || index >= items.length) {
        return
      }
      const _usedMap = this._usedMap
      const item = items[index]
      const key = this.getItemViewKey(item)
      let view = _usedMap.get(key)
      this._itemSizes[index] = undefined
      if (!view) {
        return
      }
      view.nr.size = undefined
      const viewIndex = this.itemViews.indexOf(view)
      console.log('清除', index, view)
      this.$set(this.itemViews, viewIndex, view)
    },

    handleScroll(event) {
      this.updateVisibleItems()
    },
    handleResize() {
      let itemWrapperBound = this.$refs.ScrollForWrapper?.getBoundingClientRect()
      if (!itemWrapperBound) {
        return
      }
      this.itemWrapperBound = {
        left: itemWrapperBound.left - this.buffer,
        right: itemWrapperBound.right + this.buffer,
        top: itemWrapperBound.top - this.buffer,
        bottom: itemWrapperBound.bottom + this.buffer,
      }
    },

    initPool() {
      this._usedMap = new Map()
      this._unusedList = []
      this._itemSizes = []
    },

    getBlankViewStyle(index, isComponent) {
      if (isComponent && !this.isExtraStyle) {
        return
      }
      let itemStyle = this._itemSizes[index]
      if (!itemStyle) {
        return
      }
      return `min-height: ${itemStyle.height}px;`
      // return `min-height: ${itemStyle.height}px;min-width: ${itemStyle.width}px`
    },

    getItemViewKey(item) {
      return this.itemKey ? item[this.itemKey] : item
    },

    updateVisibleItems(isCheckItem) {
      const items = this.items
      const _usedMap = this._usedMap
      const _unusedList = this._unusedList
      const itemViews = this.itemViews

      // 根据当前展示状态获取 start、end
      let { startIndex, endIndex } = this.getStartAndEnd()
      if (startIndex == this.startIndex && endIndex == this.endIndex) {
        return
      }
      for (let i = 0; i < itemViews.length; i++) {
        const view = itemViews[i]
        if (!view.nr.isUsed) {
          continue
        }
        // 处理正在使用的
        if (isCheckItem) {
          // 校验视口中 item 是否存在
          view.nr.index = items.indexOf(view.item)
        }
        if (view.nr.index === -1 || view.nr.index < startIndex || view.nr.index > endIndex) {
          if (view.nr.index !== -1) {
            // 卸载时获取宽高
            // 通过 dom 的 id 属性获取到实际 dom 宽高信息
            let component = document.getElementById(view.nr.id)
            // component.style = ''
            this._itemSizes[view.nr.index] = {
              width: component.offsetWidth,
              height: component.offsetHeight,
            }
          }
          this.unuseView(view)
        }
      }

      for (let i = startIndex; i <= endIndex; i++) {
        const item = items[i]
        const key = this.getItemViewKey(item)
        let view = _usedMap.get(key)
        // console.log('获取', key, view)
        if (!view) {
          if (_unusedList && _unusedList.length) {
            // 复用未展示节点
            view = _unusedList.pop()
            view.item = item
            view.nr.index = i
            view.nr.key = key
            view.nr.isUsed = true
            view.nr.size = this.isExtraStyle ? this._itemSizes[i] : undefined
            // console.log('复用', key, view)
          } else {
            view = this.addView(itemViews, key, i, item)
            // console.log('创建', key, view)
          }
          _usedMap.set(key, view)
        } else {
          view.nr.isUsed = true
        }
      }

      this.itemViews.sort((a, b) => a.nr.index - b.nr.index)

      // 更新索引范围
      this.startIndex = startIndex
      this.endIndex = endIndex
    },

    addView(views, key, index, item) {
      const view = {
        item,
      }
      const nonReactive = {
        id: componentIdPrefix + ++uid,
        index,
        key,
        isUsed: true,
        size: this.isExtraStyle ? this._itemSizes[index] : undefined,
      }
      Object.defineProperty(view, 'nr', {
        configurable: false,
        value: nonReactive,
      })
      views.push(view)
      return view
    },

    unuseView(view) {
      view.nr.isUsed = false
      const _unusedList = this._unusedList
      const _usedMap = this._usedMap
      _unusedList.push(view)
      _usedMap.delete(view.nr.key)
    },

    getVisibleChildren(index) {
      const divElement = this.$refs.ScrollForWrapper
      if (isNaN(index)) {
        return divElement.getElementsByClassName('scroll-for-item')
      }
      return divElement.getElementsByClassName('scroll-for-item')?.[index]
    },

    getStartAndEnd() {
      let start = 0
      let end = this.items.length - 1
      let startIndex = 0
      const itemNodes = this.getVisibleChildren()
      if (!itemNodes || itemNodes.length == 0) {
        return { startIndex: -1, endIndex: -1 }
      }
      while (start < end) {
        let i = Math.floor((start + end) / 2)
        let itemBound = itemNodes[i]?.getBoundingClientRect()
        if (itemBound.bottom < this.itemWrapperBound.top) {
          start = i + 1
        } else {
          end = i - 1
        }
      }
      startIndex = start
      let endIndex = startIndex

      end = this.items.length - 1
      while (start < end) {
        let j = Math.floor((start + end) / 2)
        let itemBound = itemNodes[j]?.getBoundingClientRect()
        if (itemBound.bottom < this.itemWrapperBound.bottom) {
          start = j + 1
        } else {
          end = j - 1
        }
      }
      endIndex = start
      // 向前向后分别索引，不在视口的索引为止
      while (this.isDisplay(itemNodes, startIndex - 1)) {
        startIndex--
      }
      while (this.isDisplay(itemNodes, endIndex + 1)) {
        endIndex++
      }

      console.log(startIndex, endIndex, endIndex - startIndex + 1)
      // console.log('---===开始--', startIndex, endIndex)
      return { startIndex, endIndex }
    },
    isDisplay(itemNodes, i) {
      let itemBound = itemNodes[i]?.getBoundingClientRect()
      if (!itemBound) {
        return false
      }
      return !(
        itemBound.right < this.itemWrapperBound.left ||
        itemBound.left > this.itemWrapperBound.right ||
        itemBound.bottom < this.itemWrapperBound.top ||
        itemBound.top > this.itemWrapperBound.bottom
      )
    },
  },
}
</script>
<style lang="scss" scoped>
.scroll-for-wrapper {
  overflow: auto;
  height: 100%;
  width: 100%;
}
</style>
