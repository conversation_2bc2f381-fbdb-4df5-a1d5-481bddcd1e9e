<template>
  <div class="scroll-for-wrapper" ref="ScrollForWrapper" @scroll.passive="handleScroll" v-rj-scroll="scrollBinding">
    <component
      v-for="(item, index) in beforeBlanks"
      class="scroll-for-item"
      :is="blankComponent"
      :key="index"
      :item="item"
      :id="`blankIdPrefix${index}`"
      :style="getBlankViewStyle(index)"
    ></component>
    <RjScrollForItem
      v-for="itemView in itemViews"
      v-show="itemView.nr.isUsed"
      :class="itemView.nr.isUsed ? 'scroll-for-item' : undefined"
      :key="itemView.nr.id"
      :id="itemView.nr.id"
      :index="itemView.nr.index"
      :style="itemView.nr.size ? `min-height: ${itemView.nr.size.height}px` : undefined"
    >
      <slot :item="itemView.item" :index="itemView.nr.index"> </slot>
    </RjScrollForItem>
    <component
      v-for="(item, index) in afterBlanks"
      :is="blankComponent"
      class="scroll-for-item"
      :key="index + endIndex + 1"
      :item="item"
      :id="`blankIdPrefix${index + endIndex + 1}`"
      :style="getBlankViewStyle(index + endIndex + 1)"
    ></component>
  </div>
</template>

<script>
import RjBlankComponent from './virtualScroll/RjBlankComponent.vue'

// const view = {
//   nr: {
//     id: '',
//     key: '',
//     index: 0,
//   },
//   item: {},
// }

let uid = 0
const componentIdPrefix = 'c-id-'
const blankIdPrefix = 'b-id-'

export default {
  props: {
    items: {
      type: Array,
      required: true,
    },
    itemKey: {
      type: String,
    },
    scrollBinding: {
      type: Object,
    },
    buffer: {
      type: Number,
      default: 100,
    },
    blankComponent: {
      type: Object,
      default: () => {
        return RjBlankComponent
      },
    },
    isExtraStyle: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      itemViews: [],
      startIndex: -1,
      endIndex: -1,
      itemIntersectionObserver: undefined,
      itemWrapperBound: undefined,
      blankIdPrefix: blankIdPrefix,
      itemSizes: undefined,
      // 滚动位置稳定化相关状态
      isUpdatingItems: false,
      scrollPositionLock: null,
      lastScrollTime: 0,
      scrollDebounceTimer: null,
      itemResizeObservers: new Map(),
    }
  },
  computed: {
    beforeBlanks() {
      if (this.startIndex < 0) {
        return this.items.slice(0, this.items.length)
      }
      return this.items.slice(0, this.startIndex)
    },
    afterBlanks() {
      if (this.endIndex < 0) {
        return []
      }
      return this.items.slice(this.endIndex + 1)
    },
  },
  watch: {
    items: {
      handler(val, oldVal) {
        // 根据索引范围重新构建
        const itemViews = this.itemViews
        this.startIndex = -1
        this.endIndex = -1
        this._itemSizes = []
        for (let i = 0; i < itemViews.length; i++) {
          const view = itemViews[i]
          if (!view.nr.isUsed) {
            continue
          }
          this.unuseView(view)
        }
        this.$nextTick(() => {
          this.updateVisibleItems(true)
        })
      },
    },
  },
  created() {
    this.initPool()
  },
  mounted() {
    this._resizeObserver = new ResizeObserver(entries => {
      this.handleResize()
      this.updateVisibleItems()
    })
    //初始化内容
    this._resizeObserver.observe(this.$refs.ScrollForWrapper)

    this.handleResize()
  },
  beforeDestroy() {
    // 清理容器的 ResizeObserver
    if (this._resizeObserver && this.$refs.ScrollForWrapper) {
      this._resizeObserver.unobserve(this.$refs.ScrollForWrapper)
    }

    // 清理所有项目的 ResizeObserver
    this.itemResizeObservers.forEach(observer => {
      observer.disconnect()
    })
    this.itemResizeObservers.clear()

    // 清理防抖定时器
    if (this.scrollDebounceTimer) {
      clearTimeout(this.scrollDebounceTimer)
    }
  },
  methods: {
    scrollIntoView(index, behavior) {
      if (index < 0 || index >= this.items.length) {
        return
      }
      const itemNodes = this.getVisibleChildren()
      itemNodes[index]?.scrollIntoView({ behavior })
    },

    clearExtraStyle(index) {
      const items = this.items
      if (index < 0 || index >= items.length) {
        return
      }
      const _usedMap = this._usedMap
      const item = items[index]
      const key = this.getItemViewKey(item)
      let view = _usedMap.get(key)
      this._itemSizes[index] = undefined
      if (!view) {
        return
      }
      view.nr.size = undefined
      const viewIndex = this.itemViews.indexOf(view)
      console.log('清除', index, view)
      this.$set(this.itemViews, viewIndex, view)
    },

    handleScroll(event) {
      // 如果正在更新项目，忽略滚动事件以防止递归
      if (this.isUpdatingItems) {
        return
      }

      const now = Date.now()
      this.lastScrollTime = now

      // 清除之前的防抖定时器
      if (this.scrollDebounceTimer) {
        clearTimeout(this.scrollDebounceTimer)
      }

      // 使用防抖机制，避免过于频繁的更新
      this.scrollDebounceTimer = setTimeout(() => {
        // 确保这是最新的滚动事件
        if (Date.now() - this.lastScrollTime >= 16) {
          // 约60fps
          this.updateVisibleItemsWithStabilization()
        }
      }, 16)
    },
    handleResize() {
      let itemWrapperBound = this.$refs.ScrollForWrapper?.getBoundingClientRect()
      if (!itemWrapperBound) {
        return
      }
      this.itemWrapperBound = {
        left: itemWrapperBound.left - this.buffer,
        right: itemWrapperBound.right + this.buffer,
        top: itemWrapperBound.top - this.buffer,
        bottom: itemWrapperBound.bottom + this.buffer,
      }
    },

    initPool() {
      this._usedMap = new Map()
      this._unusedList = []
      this._itemSizes = []
    },

    getBlankViewStyle(index, isComponent) {
      if (isComponent && !this.isExtraStyle) {
        return
      }

      let itemStyle = this._itemSizes[index]
      if (!itemStyle) {
        // 如果没有记录的高度，尝试估算
        const estimatedHeight = this.estimateItemHeight(index)
        if (estimatedHeight > 0) {
          return `min-height: ${estimatedHeight}px;`
        }
        return
      }
      return `min-height: ${itemStyle.height}px;`
      // return `min-height: ${itemStyle.height}px;min-width: ${itemStyle.width}px`
    },

    // 估算项目高度的方法
    estimateItemHeight(index) {
      // 计算已知高度的平均值
      const knownHeights = this._itemSizes.filter(size => size && size.height > 0)
      if (knownHeights.length === 0) {
        return 0 // 没有已知高度，返回0使用默认样式
      }

      // 使用已知高度的平均值作为估算
      const averageHeight = knownHeights.reduce((sum, size) => sum + size.height, 0) / knownHeights.length

      // 可以根据需要添加更复杂的估算逻辑
      // 例如：根据内容类型、索引位置等进行调整

      return Math.round(averageHeight)
    },

    getItemViewKey(item) {
      return this.itemKey ? item[this.itemKey] : item
    },

    // 带滚动位置稳定化的可见项更新方法
    updateVisibleItemsWithStabilization() {
      this.updateVisibleItems(false, true)
    },

    updateVisibleItems(isCheckItem, withStabilization = false) {
      const items = this.items
      const _usedMap = this._usedMap
      const _unusedList = this._unusedList
      const itemViews = this.itemViews

      // 根据当前展示状态获取 start、end
      let { startIndex, endIndex } = this.getStartAndEnd()
      if (startIndex == this.startIndex && endIndex == this.endIndex) {
        return
      }

      // 如果启用稳定化，记录当前滚动位置
      let savedScrollTop = null
      if (withStabilization && this.$refs.ScrollForWrapper) {
        savedScrollTop = this.$refs.ScrollForWrapper.scrollTop
        this.isUpdatingItems = true
      }

      // 记录即将卸载的组件高度
      const heightsToRecord = []
      for (let i = 0; i < itemViews.length; i++) {
        const view = itemViews[i]
        if (!view.nr.isUsed) {
          continue
        }
        // 处理正在使用的
        if (isCheckItem) {
          // 校验视口中 item 是否存在
          view.nr.index = items.indexOf(view.item)
        }
        if (view.nr.index === -1 || view.nr.index < startIndex || view.nr.index > endIndex) {
          if (view.nr.index !== -1) {
            heightsToRecord.push({
              index: view.nr.index,
              id: view.nr.id,
            })
          }
          this.unuseView(view)
        }
      }

      // 在 DOM 更新前记录高度
      this.recordItemHeights(heightsToRecord)

      for (let i = startIndex; i <= endIndex; i++) {
        const item = items[i]
        const key = this.getItemViewKey(item)
        let view = _usedMap.get(key)
        // console.log('获取', key, view)
        if (!view) {
          if (_unusedList && _unusedList.length) {
            // 复用未展示节点
            view = _unusedList.pop()
            view.item = item
            view.nr.index = i
            view.nr.key = key
            view.nr.isUsed = true
            view.nr.size = this.isExtraStyle ? this._itemSizes[i] : undefined
            // console.log('复用', key, view)
          } else {
            view = this.addView(itemViews, key, i, item)
            // console.log('创建', key, view)
          }
          _usedMap.set(key, view)
        } else {
          view.nr.isUsed = true
        }
      }

      this.itemViews.sort((a, b) => a.nr.index - b.nr.index)

      // 更新索引范围
      this.startIndex = startIndex
      this.endIndex = endIndex

      // 如果启用稳定化，在下一帧恢复滚动位置
      if (withStabilization && savedScrollTop !== null) {
        this.$nextTick(() => {
          this.restoreScrollPosition(savedScrollTop)
        })
      }
    },

    addView(views, key, index, item) {
      const view = {
        item,
      }
      const nonReactive = {
        id: componentIdPrefix + ++uid,
        index,
        key,
        isUsed: true,
        size: this.isExtraStyle ? this._itemSizes[index] : undefined,
      }
      Object.defineProperty(view, 'nr', {
        configurable: false,
        value: nonReactive,
      })
      views.push(view)

      // 在下一帧为新组件设置 ResizeObserver
      this.$nextTick(() => {
        this.setupItemResizeObserver(view)
      })

      return view
    },

    unuseView(view) {
      view.nr.isUsed = false
      const _unusedList = this._unusedList
      const _usedMap = this._usedMap

      // 清理对应的 ResizeObserver
      if (this.itemResizeObservers.has(view.nr.id)) {
        this.itemResizeObservers.get(view.nr.id).disconnect()
        this.itemResizeObservers.delete(view.nr.id)
      }

      _unusedList.push(view)
      _usedMap.delete(view.nr.key)
    },

    // 记录组件高度的改进方法
    recordItemHeights(heightsToRecord) {
      heightsToRecord.forEach(({ index, id }) => {
        const component = document.getElementById(id)
        if (component) {
          // 确保组件完全渲染后再记录高度
          const rect = component.getBoundingClientRect()
          if (rect.height > 0) {
            this._itemSizes[index] = {
              width: component.offsetWidth,
              height: component.offsetHeight,
            }
          }
        }
      })
    },

    // 恢复滚动位置
    restoreScrollPosition(targetScrollTop) {
      if (!this.$refs.ScrollForWrapper) {
        this.isUpdatingItems = false
        return
      }

      const wrapper = this.$refs.ScrollForWrapper
      const currentScrollTop = wrapper.scrollTop

      // 如果滚动位置发生了意外变化，恢复到目标位置
      if (Math.abs(currentScrollTop - targetScrollTop) > 1) {
        wrapper.scrollTop = targetScrollTop
      }

      // 延迟重置更新标志，确保滚动事件处理完成
      setTimeout(() => {
        this.isUpdatingItems = false
      }, 50)
    },

    // 为新渲染的组件设置 ResizeObserver
    setupItemResizeObserver(view) {
      if (!view.nr.id) return

      const element = document.getElementById(view.nr.id)
      if (!element) return

      // 清理之前的观察器
      if (this.itemResizeObservers.has(view.nr.id)) {
        this.itemResizeObservers.get(view.nr.id).disconnect()
      }

      // 创建新的 ResizeObserver
      const observer = new ResizeObserver(entries => {
        entries.forEach(entry => {
          const newHeight = entry.contentRect.height
          if (newHeight > 0 && this._itemSizes[view.nr.index]) {
            const oldHeight = this._itemSizes[view.nr.index].height
            if (Math.abs(newHeight - oldHeight) > 1) {
              // 高度发生显著变化，更新记录
              this._itemSizes[view.nr.index] = {
                width: entry.contentRect.width,
                height: newHeight,
              }
              // 更新组件的 size 属性
              if (view.nr.size) {
                view.nr.size.height = newHeight
              }
            }
          }
        })
      })

      observer.observe(element)
      this.itemResizeObservers.set(view.nr.id, observer)
    },

    getVisibleChildren(index) {
      const divElement = this.$refs.ScrollForWrapper
      if (isNaN(index)) {
        return divElement.getElementsByClassName('scroll-for-item')
      }
      return divElement.getElementsByClassName('scroll-for-item')?.[index]
    },

    getStartAndEnd() {
      let start = 0
      let end = this.items.length - 1
      let startIndex = 0
      const itemNodes = this.getVisibleChildren()
      if (!itemNodes || itemNodes.length == 0) {
        return { startIndex: -1, endIndex: -1 }
      }
      while (start < end) {
        let i = Math.floor((start + end) / 2)
        let itemBound = itemNodes[i]?.getBoundingClientRect()
        if (itemBound.bottom < this.itemWrapperBound.top) {
          start = i + 1
        } else {
          end = i - 1
        }
      }
      startIndex = start
      let endIndex = startIndex

      end = this.items.length - 1
      while (start < end) {
        let j = Math.floor((start + end) / 2)
        let itemBound = itemNodes[j]?.getBoundingClientRect()
        if (itemBound.bottom < this.itemWrapperBound.bottom) {
          start = j + 1
        } else {
          end = j - 1
        }
      }
      endIndex = start
      // 向前向后分别索引，不在视口的索引为止
      while (this.isDisplay(itemNodes, startIndex - 1)) {
        startIndex--
      }
      while (this.isDisplay(itemNodes, endIndex + 1)) {
        endIndex++
      }

      console.log(startIndex, endIndex, endIndex - startIndex + 1)
      // console.log('---===开始--', startIndex, endIndex)
      return { startIndex, endIndex }
    },
    isDisplay(itemNodes, i) {
      let itemBound = itemNodes[i]?.getBoundingClientRect()
      if (!itemBound) {
        return false
      }
      return !(
        itemBound.right < this.itemWrapperBound.left ||
        itemBound.left > this.itemWrapperBound.right ||
        itemBound.bottom < this.itemWrapperBound.top ||
        itemBound.top > this.itemWrapperBound.bottom
      )
    },
  },
}
</script>
<style lang="scss" scoped>
.scroll-for-wrapper {
  overflow: auto;
  height: 100%;
  width: 100%;
}
</style>
