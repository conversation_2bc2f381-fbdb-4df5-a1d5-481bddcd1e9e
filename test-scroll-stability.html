<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>虚拟滚动稳定性测试</title>
    <script src="https://cdn.jsdelivr.net/npm/vue@2.7.9/dist/vue.js"></script>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        
        .test-container {
            height: 400px;
            border: 2px solid #ccc;
            margin: 20px 0;
        }
        
        .test-item {
            padding: 10px;
            border-bottom: 1px solid #eee;
            background: #f9f9f9;
        }
        
        .test-item.pdf-content {
            min-height: 150px;
            background: linear-gradient(45deg, #e3f2fd, #bbdefb);
        }
        
        .test-item.large-content {
            min-height: 300px;
            background: linear-gradient(45deg, #f3e5f5, #e1bee7);
        }
        
        .test-item.small-content {
            min-height: 80px;
            background: linear-gradient(45deg, #e8f5e8, #c8e6c9);
        }
        
        .scroll-info {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
        }
        
        .controls {
            margin: 10px 0;
        }
        
        button {
            margin: 5px;
            padding: 8px 16px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div id="app">
        <h1>虚拟滚动稳定性测试</h1>
        
        <div class="controls">
            <button @click="generateTestData">生成测试数据</button>
            <button @click="scrollToMiddle">滚动到中间</button>
            <button @click="scrollToBottom">滚动到底部</button>
            <button @click="clearScrollInfo">清除滚动信息</button>
        </div>
        
        <div class="scroll-info">
            <div>滚动事件次数: {{ scrollEventCount }}</div>
            <div>当前滚动位置: {{ currentScrollTop }}</div>
            <div>可见范围: {{ visibleRange }}</div>
            <div>滚动稳定性: {{ scrollStability }}</div>
        </div>
        
        <div class="test-container">
            <rj-scroll-for
                ref="scrollRef"
                :items="testItems"
                itemKey="id"
                :isExtraStyle="true"
                @scroll="onScroll"
            >
                <template v-slot="{ item, index }">
                    <div 
                        :class="['test-item', item.type + '-content']"
                        :style="{ minHeight: item.height + 'px' }"
                    >
                        <h3>{{ item.title }}</h3>
                        <p>索引: {{ index }} | ID: {{ item.id }} | 类型: {{ item.type }}</p>
                        <div v-if="item.type === 'pdf'">
                            <p>模拟 PDF 内容 - 高度可变</p>
                            <div style="height: 50px; background: #ddd; margin: 10px 0;">PDF 渲染区域</div>
                        </div>
                        <div v-else-if="item.type === 'large'">
                            <p>大型内容块</p>
                            <div style="height: 200px; background: #ddd; margin: 10px 0;">大型内容区域</div>
                        </div>
                        <div v-else>
                            <p>小型内容块</p>
                            <div style="height: 30px; background: #ddd; margin: 10px 0;">小型内容区域</div>
                        </div>
                    </div>
                </template>
            </rj-scroll-for>
        </div>
        
        <div>
            <h3>测试说明:</h3>
            <ul>
                <li>生成包含不同高度内容的测试数据</li>
                <li>观察滚动时的稳定性，特别是滚动事件计数</li>
                <li>检查滚动位置是否出现意外跳跃</li>
                <li>验证虚拟滚动切换时的平滑性</li>
            </ul>
        </div>
    </div>

    <script>
        // 注册虚拟滚动组件（简化版，用于测试）
        Vue.component('rj-scroll-for', {
            template: `
                <div 
                    class="scroll-for-wrapper" 
                    ref="ScrollForWrapper" 
                    @scroll.passive="handleScroll"
                    style="overflow: auto; height: 100%; width: 100%;"
                >
                    <div v-for="(item, index) in items" :key="item.id" class="scroll-for-item">
                        <slot :item="item" :index="index"></slot>
                    </div>
                </div>
            `,
            props: {
                items: Array,
                itemKey: String,
                isExtraStyle: Boolean
            },
            methods: {
                handleScroll(event) {
                    this.$emit('scroll', event)
                }
            }
        })

        new Vue({
            el: '#app',
            data: {
                testItems: [],
                scrollEventCount: 0,
                currentScrollTop: 0,
                visibleRange: '',
                lastScrollTime: 0,
                scrollStability: '稳定'
            },
            mounted() {
                this.generateTestData()
            },
            methods: {
                generateTestData() {
                    this.testItems = []
                    const types = ['pdf', 'large', 'small']
                    const heights = { pdf: 150, large: 300, small: 80 }
                    
                    for (let i = 0; i < 1000; i++) {
                        const type = types[i % 3]
                        this.testItems.push({
                            id: i + 1,
                            title: `测试项目 ${i + 1}`,
                            type: type,
                            height: heights[type] + Math.random() * 50 // 添加随机高度变化
                        })
                    }
                },
                
                onScroll(event) {
                    this.scrollEventCount++
                    this.currentScrollTop = Math.round(event.target.scrollTop)
                    
                    // 检测滚动稳定性
                    const now = Date.now()
                    if (now - this.lastScrollTime < 50) {
                        this.scrollStability = '不稳定 - 频繁触发'
                    } else {
                        this.scrollStability = '稳定'
                    }
                    this.lastScrollTime = now
                },
                
                scrollToMiddle() {
                    const wrapper = this.$refs.scrollRef.$refs.ScrollForWrapper
                    wrapper.scrollTop = wrapper.scrollHeight / 2
                },
                
                scrollToBottom() {
                    const wrapper = this.$refs.scrollRef.$refs.ScrollForWrapper
                    wrapper.scrollTop = wrapper.scrollHeight
                },
                
                clearScrollInfo() {
                    this.scrollEventCount = 0
                    this.scrollStability = '稳定'
                }
            }
        })
    </script>
</body>
</html>
