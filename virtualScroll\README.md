# virtualScroll

虚拟列表滚动组件，在加载大列表时使用



## 组件说明

### RecycleScroller

固定尺寸列表项滚动列表

```vue
<template>
  <RecycleScroller :items="items" :itemSize="100">
      <template v-slot="{item, index}">
        <div>{{item.value}}</div>
      </template>
  </RecycleScroller>
</template>
```



| **参数**      | **说明**                                                     | **类型** | 可选值 | 默认值 |
| ------------- | ------------------------------------------------------------ | -------- | ------ | ------ |
| items         | 列表数据                                                     | Array    |        |        |
| scrollBinding | 与 *v-rj-scroll* 指令参数对应，可用于滚动加载等              | Object   |        |        |
| itemKey       | item 数据唯一标识属性名                                      | String   |        |        |
| buffer        | 容器上下预留渲染尺寸（单位像素），即扩大容器上下部分渲染区域 | Number   |        | 100    |
| itemSize      | 列表项高度大小                                               | Number   |        |        |
| minItemSize   | 列表项最小高度                                               | Number   |        |        |
| fixedWidth    | grid 布局时设置，固定宽度                                    | Number   |        |        |
| minWidth      | grid 布局时设置，最小宽度                                    | Number   |        |        |
| columnGap     | grid 布局时设置，列间距                                      | Number   |        | 0      |



### DynamicScroller

动态尺寸列表项滚动列表，需要配合 `DynamicScrollerItem` 使用，`DynamicScrollerItem` 来动态更新实际节点的尺寸

```vue
<template>
    <DynamicScroller v-if="items" :items="items">
      <template v-slot="{ item }">
        <DynamicScrollerItem :item="item">
          <div>{{item.value}}</div>
        </DynamicScrollerItem>
      </template>
    </DynamicScroller>
</template>
```



| **参数**      | **说明**                                                     | **类型** | 可选值 | 默认值 |
| ------------- | ------------------------------------------------------------ | -------- | ------ | ------ |
| items         | 列表数据                                                     | Array    |        |        |
| scrollBinding | 与 *v-rj-scroll* 指令参数对应，可用于滚动加载等              | Object   |        |        |
| itemKey       | item 数据唯一标识属性名                                      | String   |        |        |
| buffer        | 容器上下预留渲染尺寸（单位像素），即扩大容器上下部分渲染区域 | Number   |        | 100    |
| itemSize      | 列表项高度大小                                               | Number   |        |        |
| minItemSize   | 列表项最小高度                                               | Number   |        |        |



### ScrollFor

使用`占位组件`实现的**滚动列表组件**。仅在容器需要展示的部分（包括容器上下预留渲染尺寸）渲染`实际组件`，其余部分使用`占位组件`代替。

```vue
<template>
  <ScrollFor
      :items="items"
      itemKey="key"
      :blankComponent="blankComponent"
      :scrollBinding="{ onBottom }"
    >
      <template v-slot="{ item, index }">
        <Item :item="item"></Item>
      </template>
  </ScrollFor>
</template>
```



| **参数**       | **说明**                                                     | **类型** | 可选值 | 默认值                          |
| -------------- | ------------------------------------------------------------ | -------- | ------ | ------------------------------- |
| items          | 列表数据                                                     | Array    |        |                                 |
| scrollBinding  | 与 *v-rj-scroll* 指令参数对应，可用于滚动加载等              | Object   |        |                                 |
| itemKey        | item 数据唯一标识属性名                                      | String   |        |                                 |
| buffer         | 容器上下预留渲染尺寸（单位像素），即扩大容器上下部分渲染区域 | Number   |        | 100                             |
| blankComponent | 占位组件                                                     | Object   |        | RjBlankComponent（空 div 组件） |
| isExtraStyle   | 外界是否控制实际组件的额外样式，即由外界控制时，组件会为实际组件添加最小高度的样式（能读取到组件实际样式时） | Boolean  |        | true                            |